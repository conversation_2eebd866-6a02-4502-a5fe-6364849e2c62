-- AlterEnum
ALTER TYPE "MessageType" ADD VALUE 'STREAMING_CHUNK';

-- AlterTable
ALTER TABLE "Message" ADD COLUMN     "chunkIndex" INTEGER,
ADD COLUMN     "metadata" JSONB,
ADD COLUMN     "parentMessageId" TEXT,
ADD COLUMN     "totalChunks" INTEGER;

-- AddForeignKey
ALTER TABLE "Message" ADD CONSTRAINT "Message_parentMessageId_fkey" FOREIGN KEY ("parentMessageId") REFERENCES "Message"("id") ON DELETE SET NULL ON UPDATE CASCADE;
